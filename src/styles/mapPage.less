.map-cont {
    /deep/.ivu-card-body {
        padding: 0;
        position: relative;

        .plate-bg {
            background: #FFFFFF;
            box-shadow: -2px 1px 13px rgba(83, 117, 167, 0.2);
            border-radius: 4px;
        }

        .face-plate-cont {
            position: absolute;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: space-between;
            z-index: 1;
            pointer-events: none;

            .left-plate {
                width: 256px;
                padding: 16px;
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                pointer-events: auto;

                .area-tree {
                    height: 55%;
                    overflow: hidden;
                    padding: 8px;
                    display: flex;
                    flex-direction: column;
                    .ivu-tree-title {
                        &:hover {
                            background: none;
                            cursor: auto;
                        }
                    }
                    .ivu-tree-title-selected{
                        background: none;
                        cursor: auto;
                    }
                }

                .device-cont {
                    flex: 1;
                    overflow: hidden;
                    display: flex;
                    flex-direction: column;
                    padding: 8px;

                }
            }

            .right-plate {
                width: 360px;
                padding: 16px;
                display: flex;
                flex-direction: column;
                row-gap: 8px;
                position: relative;

                .full-op {
                    pointer-events: auto;
                    cursor: pointer;
                    position: absolute;
                    width: 36px;
                    height: 36px;
                    left: -36px;
                    top: 22px;
                    background: #FFFFFF;
                    border: 1px solid #E5E6EB;
                    border-radius: 6px;
                    text-align: center;
                    line-height: 34px;
                    color: #4E627E;
                    font-size: 18px;
                }
                .device-info-cont {
                    flex: 1;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;

                    .right-tab-title {
                        pointer-events: auto;
                        background: #fff;
                        box-shadow: -2px 2px 13px rgba(78, 89, 105, 0.2);
                        border-radius: 4px;
                        height: 48px;
                        display: flex;
                        align-items: center;
                        .toggle {
                            height: 32px;
                            width: 32px;
                            text-align: center;
                            cursor: pointer;
                        }

                    }
                    .right-tab-body {
                        pointer-events: auto;
                        margin-top: 8px;
                        overflow: hidden;
                        background: #fff;
                        box-shadow: -2px 2px 13px rgba(78, 89, 105, 0.2);
                        border-radius: 4px;
                        // padding: 16px;
                        flex: 1;
                        &.hide-tab-box {
                            display: none!important;
                        }
                        .tab-cont {
                            height: 100%;
                            overflow: hidden;
                            .scorll-map-cont {
                                height:100%;
                                &.manhole-cover-right {
                                    padding: 8px 8px 24px 16px;
                                    margin-right: 4px;
                                }
                            }
                        }

                    }
                    .device-overview {
                        height:91px;
                        display: flex;
                        pointer-events: auto;
                        .device-num {
                            width: 90px;
                            font-size: 24px;
                            padding-top: 15px;
                            display: flex;
                            flex-direction: column;
                            font-weight: 600;
                            .online-num {
                                white-space:nowrap;
                                font-weight: 400;
                                font-size: 12px;
                                color: #4E627E;
                                color: @title-color  ;
                                padding-top: 1px;
                                span {
                                    color: @primary-color;
                                    margin-left: 8px;
                                }
                            }
                        }
                        .device-echart {
                            flex: 1;
                        }
                    }
                    .online-overview {
                        height:230px;
                    }
                    .alarm-overview {
                        flex:1;
                        display: flex;
                        flex-direction: column;
                        overflow: hidden;
                        .alarm-info {
                            // margin-top: 7px;
                            height: 62px;
                            display: flex;
                            justify-content: space-evenly;
                            align-items: center;
                            color: @input-placeholder-color;
                            font-weight: 400;
                            font-size: 14px;
                            line-height: 24px;
                            .title-icon {
                                height:34px;
                                width: 34px;
                                display: block;
                            }
                            .num {
                                color: @title-color;
                                font-size: 20px;
                                line-height: 28px;
                                text-align: center;
                                font-weight: 500;
                            }
                        }
                        .alarm-list {
                            flex: 1;
                            overflow-y: auto;
                            .alarm-item {
                                display: grid;
                                grid-template-columns: 20% 30% 51%;
                                border-bottom: 1px solid @border-color-split;
                                height: 40px;
                                align-items: center;
                                padding: 0 16px;
                                justify-content: space-between;
                                overflow: hidden;
                                &>div {
                                    display:flex;
                                    align-items: center;
                                    overflow: hidden;
                                    &>div{
                                        flex:1;
                                        overflow: hidden;
                                    }
                                }
                                .point {
                                    width: 10px;
                                    height: 10px;
                                    border-radius: 100%;
                                    display: inline-block;
                                    margin-right: 10px;
                                }
                                .tag-cont {
                                    width: 64px;
                                }
                            }
                        }
                    }
                }
                .details-container {
                    position: relative;
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    height: 100%;
                    pointer-events: auto;
                    .head-cont {
                        background: rgba(194, 198, 206, 0.6);
                        height: 56px;
                        &.normal {
                            background: #165DFF;
                            .icon-img {
                                // background: #FFFFFF url('./images/icon_manholecover_009.png') no-repeat center;
                                background-size: 40px 40px;
                                &:after {
                                    background: #165DFF;

                                }

                            }
                        }
                        &.online {
                            background: #E8FFEA;
                            .icon-img {
                                // background: #FFFFFF url('./images/icon_manholecover_009.png') no-repeat center;
                                background-size: 40px 40px;
                                &:after {
                                    background: #00B42A;

                                }

                            }
                        }
                        .icon-img {
                            height: 50px;
                            width: 50px;
                            // background: #FFFFFF url('./images/icon_manholecover_001.png') no-repeat center;
                            background-size: 40px 40px;
                            border-radius: 100%;
                            position: absolute;
                            left: 50%;
                            transform: translate(-50%, 35px);
                            display: flex;
                            align-items:center;
                            justify-content: center;
                            background-color: #fff;
                            img.top-icon{
                                display: block;
                                height:15px;
                                width: 15px;
                            }
                            &:after {
                                height: 32px;
                                width: 32px;
                                background: #C2C6CE;
                                border-radius:100%;
                                position: absolute;
                                content: '';
                                z-index: -1;
                            }
                        }
                        .status-tag {
                            width: 40px;
                            position: absolute;
                            left: 50%;
                            top: 68px;
                            transform: translate(-50%, 0);
                        }
                    }
                    .name {
                        font-weight: 500;
                        font-size: 16px;
                        line-height: 24px;
                        color: @title-color;
                        text-align: center;
                        padding: 40px 0 0px;
                    }
                    .code {
                        color: @title-color;
                        font-weight: 400;
                        font-size: 14px;
                        line-height: 22px;
                        text-align: center;
                        margin-bottom: 14px;
                    }
                    .tab-body {
                        flex: 1;
                        overflow: hidden;
                        padding: 16px;
                    }
                    .ivu-steps.ivu-steps-small .ivu-steps-head-inner {
                        height:10px;
                        width:10px;
                        background-color:#165DFF;
                        margin-top: 4px;
                        span {
                            display: none;
                        }
                    }
                    .ivu-steps-vertical.ivu-steps-small {
                        padding-top: 24px;
                        .ivu-steps-tail {
                            left: 5px;
                            padding: 18px 0 0px 0;
                        }
                        .ivu-steps-item .ivu-steps-tail > i {
                            background-color: #165DFF;
                        }
                        .ivu-steps-content{
                            white-space: pre-line;
                        }
                    }
                }
            }
        }


        .device-info-list {
            padding: 0px 8px 20px 8px;
        }
        .alarm-info-list {
            padding: 10px 8px;
            background: #F8FAFB;
            min-height: 120px;
        }
    }



}
.container-map {
    width: 100%;
    height: ~'calc(100vh - 110px)';
    &.full-map {
        height: 100vh;
    }
    /deep/.content-window-card {
        border-radius: 6px;
        // border: 0.5px solid #FFF;
        background: linear-gradient(304deg, rgba(253, 254, 255, 0.60) -6.04%, rgba(244, 247, 252, 0.60) 85.2%);
        backdrop-filter: blur(5px);
        padding: 10px;
        max-height:200px;
        overflow-y: auto;
        // transform: translate(~'calc(100% - 60px)', ~'calc(100% + 40px)');
        .window-card-item {
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.90);
            background: rgba(255, 255, 255, 0.90);
            box-shadow: 6px 0px 20px 0px rgba(34, 87, 188, 0.10);
            height: 36px;
            padding: 0 16px;
            margin-bottom: 4px;
            cursor: pointer;
            &:hover {
                border: 1px solid #BEDAFF;
                box-shadow: 6px 0px 20px 0px rgba(34, 87, 188, 0.10);
            }
            &.actived {
                border: 1px solid #165DFF;
                box-shadow: 6px 0px 20px 0px rgba(34, 87, 188, 0.10);
            }
            .name {
                color:#1E2A55;
                font-size: 12px;
                height: 20px;
                line-height: 20px;
            }
            .code {
                color:#798799;
                font-size: 12px;
                height: 22px;
                line-height: 22px;
                height: 16px;
                line-height: 16px;
            }
        }
    }
    /deep/.amap-marker-label {
        z-index: -1;
    }
    /deep/.marker-label-box {
        max-width: 150px;
        height: 31px;
        border-radius: 15px;
        white-space: nowrap;
        min-width: 80px;
        transform: translate(-30px, 0);
        background: #FFFFFF;
        box-shadow: 0px 1px 6px rgba(61, 103, 175, 0.2);
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        line-height: 31px;
        // align-items: center;
        padding-left: 30px;
        font-size: 12px;
        padding-right: 8px;
        &.label-box {
            z-index: -1;
            position: absolute;
            transform: translate(10px, -35px) !important;
            display: flex;
            align-items: center;
            column-gap: 4px;
            span.name {
                flex: 1;
                max-width: 90px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
            }
            span.num {
                height: 18px;
                width: 18px;
                background: #FFD233;
                border-radius: 100%;
                line-height: 18px;
                text-align: center;
            }
        }
    }
    /deep/.marker-icon {
            height: 40px;
            width: 40px;
            position: relative;
            background: url('./images/icon_map_001.png') no-repeat center;
            background-size: 40px 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            &:hover {
                background: url('./images/icon_map_002.png') no-repeat center;
                background-size: 40px 40px;
            }
            &.actived {
                background: url('./images/icon_map_003.png') no-repeat center;
                background-size: 40px 40px;
            }
            &.alarm-state {
                background: url('./images/icon_map_004.png') no-repeat center;
                background-size: 40px 40px;
                &:hover {
                    background: url('./images/icon_map_005.png') no-repeat center;
                    background-size: 40px 40px;
                }
                &.actived {
                    background: url('./images/icon_map_006.png') no-repeat center;
                    background-size: 40px 40px;
                }
            }
            &.green-state{
                background: url('./images/icon_map_007.png') no-repeat center;
                background-size: 40px 40px;
                &:hover {
                    background: url('./images/icon_map_008.png') no-repeat center;
                    background-size: 40px 40px;
                }
                &.actived {
                    background: url('./images/icon_map_009.png') no-repeat center;
                    background-size: 40px 40px;
                }

            }
        .marker-status {
            position: absolute;
            font-weight: 500;
            font-size: 12px;
            height:20px;
            width: 20px;
            font-size: 12px;
            border-radius:100%;
            border: 1px solid #FFFFFF;
            right: -3px;
            top: 0px;
            text-align: center;
            &.hide-tag{
                display: none;
            }
            &.open {
                background: #FFD233;
            }
            &.closed {
                background: #D9D9D9;
            }
        }
        img {
            // margin-top:-5px;
            height: 16px;
            width: 16px;
        }
    }
    /deep/.marker-label {
        padding: 8px;
        background: linear-gradient(304.17deg, rgba(253, 254, 255, 0.6) -6.04%, rgba(244, 247, 252, 0.6) 85.2%);
        backdrop-filter: blur(5px);
        border-radius: 6px;
        min-width: 120px;
        transform: translate(-25%, -2px);
        white-space:nowrap;
        &>div{
            display: flex;
            background: #fff;
            height:32px;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 6px 0px 20px rgba(34, 87, 188, 0.1);
            border-radius: 4px;
            align-items: center;
            justify-content: space-between;
            padding: 8px;
            column-gap: 8px;
            .num {
                font-weight: bold;
            }
            &.total-num {
                color: #165DFF;
                margin-bottom: 8px;
            }
            &.spare-num {
                color: #00B42A;
            }
        }
    }
}
/deep/.device-info {
    border: 1px solid #E0E6F1;
    border-radius: 4px;
    margin-top: 8px;
    padding: 8px;
    cursor: pointer;
    &:hover{
        background: #F3F7FB;
        border: 1px solid #E0E6F1;
        border-radius: 4px;
    }
    &.actived {
        border: 1px solid #165DFF;
        box-shadow: -2px 1px 13px rgba(83, 117, 167, 0.2);
    }

    .device-name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 20px;
        margin-bottom: 2px;

        .device-status {
            display: flex;
        }
        .name {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
    }

    .device-code {
        color: @input-placeholder-color;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
    }
}
